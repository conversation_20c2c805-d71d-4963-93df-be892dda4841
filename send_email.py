import smtplib
from email.message import EmailMessage
import ssl
import socket
import os
import json
import re
import subprocess
import sys
from datetime import datetime, timedelta

# 1. 邮箱登录信息
smtp_server = 'smtp.qq.com'
smtp_port = 465  # SSL端口
sender_email = '<EMAIL>'
sender_password = 'xmetqrgiiobqcabd'  # QQ邮箱的授权码

# 2. 收件人信息
receiver_email = '<EMAIL>'

# 3. 自动执行爬虫脚本函数
def run_crawler_scripts():
    """依次执行各平台的爬虫脚本"""
    crawlers = [
        {
            'name': 'iOS爬虫',
            'script': 'APPLE/ios_appstore_crawler.py',
            'expected_file': 'APPLE/ios_apps_info-{}.json'
        },
        {
            'name': '华为爬虫',
            'script': 'HUAWEI/huawei_appstore_crawler.py',
            'expected_file': 'HUAWEI/huawei_apps_info-{}.log'
        },
        {
            'name': 'OPPO爬虫',
            'script': 'OPPO/oppo_appstore_crawler.py',
            'expected_file': 'OPPO/oppo_apps_version-{}.log'
        },
        {
            'name': 'VIVO爬虫',
            'script': 'VIVO/vivo_appstore_crawler.py',
            'expected_file': 'VIVO/vivo_apps_info-{}.json'
        },
        {
            'name': '小米爬虫',
            'script': 'XIAOMI/xiaomi_appstore_crawler.py',
            'expected_file': 'XIAOMI/xiaomi_apps_info-{}.json'
        },
        {
            'name': 'HONOR爬虫',
            'script': 'HONOR/honor_appstore_crawler.py',
            'expected_file': 'HONOR/honor_apps_info-{}.log'
        }
    ]

    current_date = datetime.now()
    date_str = f"{current_date.year}-{current_date.month}-{current_date.day}"
    oppo_date_str = f"{current_date.year}-{current_date.month:02d}-{current_date.day:02d}"

    print("=== 开始执行爬虫脚本 ===")

    for crawler in crawlers:
        print(f"\n正在执行 {crawler['name']}...")

        # 检查脚本文件是否存在
        if not os.path.exists(crawler['script']):
            print(f"[ERROR] 脚本文件不存在: {crawler['script']}")
            continue

        try:
            # 获取脚本所在目录和脚本名
            script_dir = os.path.dirname(crawler['script'])
            script_name = os.path.basename(crawler['script'])

            # 设置环境变量强制使用UTF-8编码
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'

            # 在脚本所在目录中执行爬虫脚本
            result = subprocess.run([sys.executable, script_name],
                                  capture_output=True,
                                  text=True,
                                  timeout=300,  # 5分钟超时
                                  cwd=script_dir,
                                  env=env,
                                  encoding='utf-8',
                                  errors='ignore')

            if result.returncode == 0:
                print(f"[OK] {crawler['name']} 执行成功")

                # 检查预期的数据文件是否生成
                if crawler['name'] == 'OPPO爬虫':
                    expected_file = crawler['expected_file'].format(oppo_date_str)
                else:
                    expected_file = crawler['expected_file'].format(date_str)

                if os.path.exists(expected_file):
                    print(f"[OK] 数据文件已生成: {expected_file}")
                else:
                    print(f"⚠ 数据文件未找到: {expected_file}")
            else:
                print(f"[ERROR] {crawler['name']} 执行失败")
                print(f"错误输出: {result.stderr}")

        except subprocess.TimeoutExpired:
            print(f"[ERROR] {crawler['name']} 执行超时（超过5分钟）")
        except Exception as e:
            print(f"[ERROR] {crawler['name']} 执行异常: {e}")

        # 在爬虫之间添加短暂延迟
        import time
        time.sleep(2)

    print("\n=== 爬虫脚本执行完成 ===\n")

# 4. 数据解析函数
def parse_ios_data(filename):
    """解析iOS JSON数据"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        versions = {}
        for app_name in ['安吉星', 'MyCadillac', 'MyChevy', 'iBuick']:
            if app_name in data and data[app_name].get('version'):
                versions[app_name] = data[app_name]['version']
            else:
                versions[app_name] = '未找到'
        return versions
    except Exception as e:
        print(f"解析iOS数据失败: {e}")
        return {'安吉星': '未找到', 'MyCadillac': '未找到', 'MyChevy': '未找到', 'iBuick': '未找到'}

def parse_huawei_log(filename):
    """解析华为LOG数据"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()

        versions = {}
        app_mapping = {'安吉星': '安吉星', 'iBuick': 'iBuick', 'MyCadillac': 'MyCadillac', 'MyChevy': 'MyChevy'}

        for app_name in app_mapping.keys():
            pattern = rf'\[OK\] {re.escape(app_name)}\s+版本:\s*([0-9]+\.[0-9]+(?:\.[0-9]+)*)'
            match = re.search(pattern, content)
            if match:
                versions[app_name] = match.group(1).strip()
            else:
                versions[app_name] = '未找到'
        return versions
    except Exception as e:
        print(f"解析华为数据失败: {e}")
        return {'安吉星': '未找到', 'MyCadillac': '未找到', 'MyChevy': '未找到', 'iBuick': '未找到'}

def parse_oppo_log(filename):
    """解析OPPO LOG数据"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()

        versions = {}
        app_mapping = {'安吉星': '安吉星', 'iBuick': 'iBuick', 'MyCadillac': 'MyCadillac', 'MyChevy': 'MyChevy'}

        for app_name in app_mapping.keys():
            pattern = rf'\[OK\] {re.escape(app_name)}\s+版本:\s*([0-9]+\.[0-9]+(?:\.[0-9]+)*)'
            match = re.search(pattern, content)
            if match:
                versions[app_name] = match.group(1).strip()
            else:
                versions[app_name] = '未找到'
        return versions
    except Exception as e:
        print(f"解析OPPO数据失败: {e}")
        return {'安吉星': '未找到', 'MyCadillac': '未找到', 'MyChevy': '未找到', 'iBuick': '未找到'}

def parse_vivo_data(filename):
    """解析VIVO JSON数据"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        versions = {}
        for app_name in ['安吉星', 'MyCadillac', 'MyChevy', 'iBuick']:
            if app_name in data and data[app_name].get('version'):
                versions[app_name] = data[app_name]['version']
            else:
                versions[app_name] = '未找到'
        return versions
    except Exception as e:
        print(f"解析VIVO数据失败: {e}")
        return {'安吉星': '未找到', 'MyCadillac': '未找到', 'MyChevy': '未找到', 'iBuick': '未找到'}

def parse_xiaomi_data(filename):
    """解析小米JSON数据"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        versions = {}
        for app_name in ['安吉星', 'MyCadillac', 'MyChevy', 'iBuick']:
            if app_name in data and data[app_name].get('version'):
                versions[app_name] = data[app_name]['version']
            else:
                versions[app_name] = '未找到'
        return versions
    except Exception as e:
        print(f"解析小米数据失败: {e}")
        return {'安吉星': '未找到', 'MyCadillac': '未找到', 'MyChevy': '未找到', 'iBuick': '未找到'}

def parse_honor_log(filename):
    """解析HONOR LOG数据"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()

        versions = {}
        app_mapping = {'安吉星': '安吉星', 'iBuick': 'iBuick', 'MyCadillac': 'MyCadillac', 'MyChevy': 'MyChevy'}

        for app_name in app_mapping.keys():
            pattern = rf'\[OK\] {re.escape(app_name)}\s+版本:\s*([0-9]+\.[0-9]+(?:\.[0-9]+)*)'
            match = re.search(pattern, content)
            if match:
                versions[app_name] = match.group(1).strip()
            else:
                versions[app_name] = '未找到'
        return versions
    except Exception as e:
        print(f"解析HONOR数据失败: {e}")
        return {'安吉星': '未找到', 'MyCadillac': '未找到', 'MyChevy': '未找到', 'iBuick': '未找到'}

def parse_info_file(filename):
    """解析INFO文件中的版本信息"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()

        versions = {}
        # 查找第一个表格（当日数据）
        lines = content.split('\n')
        table_started = False

        for line in lines:
            if '应用名称' in line and 'APPLE' in line:
                table_started = True
                continue
            if table_started and '======' in line:
                break
            if table_started and any(app in line for app in ['安吉星', 'MyCadillac', 'MyChevy', 'iBuick']):
                parts = line.split()
                if len(parts) >= 7:  # 现在需要7列（包含HONOR）
                    app_name = parts[0]
                    app_versions = {
                        'APPLE': parts[1],
                        'HUAWEI': parts[2],
                        'OPPO': parts[3],
                        'VIVO': parts[4],
                        'XIAOMI': parts[5],
                        'HONOR': parts[6]
                    }
                    versions[app_name] = app_versions
                elif len(parts) >= 6:  # 兼容旧格式（没有HONOR列）
                    app_name = parts[0]
                    app_versions = {
                        'APPLE': parts[1],
                        'HUAWEI': parts[2],
                        'OPPO': parts[3],
                        'VIVO': parts[4],
                        'XIAOMI': parts[5],
                        'HONOR': '未找到'
                    }
                    versions[app_name] = app_versions

        return versions
    except Exception as e:
        print(f"解析INFO文件失败: {e}")
        return {}

def get_current_versions():
    """获取当前所有平台的版本信息"""
    current_date = datetime.now()
    date_str = f"{current_date.year}-{current_date.month}-{current_date.day}"

    # 文件路径
    ios_file = f"APPLE/ios_apps_info-{date_str}.json"
    huawei_file = f"HUAWEI/huawei_apps_info-{date_str}.log"
    oppo_file = f"OPPO/oppo_apps_version-{current_date.year}-{current_date.month:02d}-{current_date.day:02d}.log"
    vivo_file = f"VIVO/vivo_apps_info-{date_str}.json"
    xiaomi_file = f"XIAOMI/xiaomi_apps_info-{date_str}.json"
    honor_file = f"HONOR/honor_apps_info-{date_str}.log"

    # 解析各平台数据
    ios_versions = parse_ios_data(ios_file)
    huawei_versions = parse_huawei_log(huawei_file)
    oppo_versions = parse_oppo_log(oppo_file)
    vivo_versions = parse_vivo_data(vivo_file)
    xiaomi_versions = parse_xiaomi_data(xiaomi_file)
    honor_versions = parse_honor_log(honor_file)

    # 合并数据
    all_versions = {}
    for app_name in ['安吉星', 'MyCadillac', 'MyChevy', 'iBuick']:
        all_versions[app_name] = {
            'APPLE': ios_versions.get(app_name, '未找到'),
            'HUAWEI': huawei_versions.get(app_name, '未找到'),
            'OPPO': oppo_versions.get(app_name, '未找到'),
            'VIVO': vivo_versions.get(app_name, '未找到'),
            'XIAOMI': xiaomi_versions.get(app_name, '未找到'),
            'HONOR': honor_versions.get(app_name, '未找到')
        }

    return all_versions

# 4. 获取版本数据
current_date = datetime.now()
yesterday = current_date - timedelta(days=1)

today_str = f"{current_date.year}-{current_date.month}-{current_date.day}"
yesterday_str = f"{yesterday.year}-{yesterday.month}-{yesterday.day}"

# 首先执行所有爬虫脚本生成数据文件
run_crawler_scripts()

# 获取当前版本数据
current_versions = get_current_versions()

# 获取昨日版本数据（从INFO文件）
yesterday_info_file = f"INFO/info-{yesterday_str}.txt"
yesterday_versions = parse_info_file(yesterday_info_file)

print("当前版本数据:")
for app, versions in current_versions.items():
    print(f"{app}: {versions}")

print("\n昨日版本数据:")
for app, versions in yesterday_versions.items():
    print(f"{app}: {versions}")

# 5. 生成当日INFO文件
def generate_info_file(current_versions, yesterday_versions, today_str, yesterday_str):
    """生成当日的INFO文件"""
    try:
        # 确保INFO目录存在
        os.makedirs('INFO', exist_ok=True)

        info_filename = f"INFO/info-{today_str}.txt"

        with open(info_filename, 'w', encoding='utf-8') as f:
            f.write("您好！\n\n")
            f.write(f"以下是 {today_str} 的应用版本对比表：\n\n")
            f.write(f"应用版本对比表 - {today_str}\n")
            f.write("================================================================\n")
            f.write("应用名称         APPLE    HUAWEI   OPPO     VIVO     XIAOMI   HONOR\n")
            f.write("----------------------------------------------------------------\n")

            # 写入当日版本数据
            for app_name in ['安吉星', 'MyCadillac', 'MyChevy', 'iBuick']:
                versions = current_versions.get(app_name, {})
                apple_ver = versions.get('APPLE', '未找到')
                huawei_ver = versions.get('HUAWEI', '未找到')
                oppo_ver = versions.get('OPPO', '未找到')
                vivo_ver = versions.get('VIVO', '未找到')
                xiaomi_ver = versions.get('XIAOMI', '未找到')
                honor_ver = versions.get('HONOR', '未找到')

                # 格式化输出，保持对齐
                f.write(f"{app_name:<12} {apple_ver:<8} {huawei_ver:<8} {oppo_ver:<8} {vivo_ver:<8} {xiaomi_ver:<8} {honor_ver}\n")

            f.write("================================================================\n\n")

            # 如果有昨日数据，添加昨日版本对比表
            if yesterday_versions:
                f.write(f"以下是 {yesterday_str} 的应用版本对比表：\n\n")
                f.write(f"应用版本对比表 - {yesterday_str}\n")
                f.write("================================================================\n")
                f.write("应用名称         APPLE    HUAWEI   OPPO     VIVO     XIAOMI   HONOR\n")
                f.write("----------------------------------------------------------------\n")

                for app_name in ['安吉星', 'MyCadillac', 'MyChevy', 'iBuick']:
                    versions = yesterday_versions.get(app_name, {})
                    apple_ver = versions.get('APPLE', '未找到')
                    huawei_ver = versions.get('HUAWEI', '未找到')
                    oppo_ver = versions.get('OPPO', '未找到')
                    vivo_ver = versions.get('VIVO', '未找到')
                    xiaomi_ver = versions.get('XIAOMI', '未找到')
                    honor_ver = versions.get('HONOR', '未找到')

                    f.write(f"{app_name:<12} {apple_ver:<8} {huawei_ver:<8} {oppo_ver:<8} {vivo_ver:<8} {xiaomi_ver:<8} {honor_ver}\n")

                f.write("================================================================\n\n")

            f.write("此邮件由Python自动化脚本发送\n")

        print(f"[OK] 已生成当日INFO文件: {info_filename}")
        return info_filename

    except Exception as e:
        print(f"[ERROR] 生成INFO文件失败: {e}")
        return None

# 生成当日INFO文件
info_file_path = generate_info_file(current_versions, yesterday_versions, today_str, yesterday_str)

# 6. 创建邮件内容
msg = EmailMessage()
msg['Subject'] = f'应用版本对比表 - {today_str}'
msg['From'] = sender_email
msg['To'] = receiver_email

# 生成纯文本版本
def generate_text_body(current_versions, yesterday_versions, today_str, yesterday_str):
    text = f"""您好！

以下是 {today_str} 的应用版本对比表：

应用版本对比表 - {today_str}
================================================================
应用名称         APPLE    HUAWEI   OPPO     VIVO     XIAOMI   HONOR
----------------------------------------------------------------
"""

    for app_name in ['安吉星', 'MyCadillac', 'MyChevy', 'iBuick']:
        versions = current_versions.get(app_name, {})
        apple_ver = versions.get('APPLE', '未找到')
        huawei_ver = versions.get('HUAWEI', '未找到')
        oppo_ver = versions.get('OPPO', '未找到')
        vivo_ver = versions.get('VIVO', '未找到')
        xiaomi_ver = versions.get('XIAOMI', '未找到')
        honor_ver = versions.get('HONOR', '未找到')

        text += f"{app_name:<12} {apple_ver:<8} {huawei_ver:<8} {oppo_ver:<8} {vivo_ver:<8} {xiaomi_ver:<8} {honor_ver}\n"

    text += "================================================================\n\n"

    if yesterday_versions:
        text += f"以下是 {yesterday_str} 的应用版本对比表：\n\n"
        text += f"应用版本对比表 - {yesterday_str}\n"
        text += "================================================================\n"
        text += "应用名称         APPLE    HUAWEI   OPPO     VIVO     XIAOMI   HONOR\n"
        text += "----------------------------------------------------------------\n"

        for app_name in ['安吉星', 'MyCadillac', 'MyChevy', 'iBuick']:
            versions = yesterday_versions.get(app_name, {})
            apple_ver = versions.get('APPLE', '未找到')
            huawei_ver = versions.get('HUAWEI', '未找到')
            oppo_ver = versions.get('OPPO', '未找到')
            vivo_ver = versions.get('VIVO', '未找到')
            xiaomi_ver = versions.get('XIAOMI', '未找到')
            honor_ver = versions.get('HONOR', '未找到')

            text += f"{app_name:<12} {apple_ver:<8} {huawei_ver:<8} {oppo_ver:<8} {vivo_ver:<8} {xiaomi_ver:<8} {honor_ver}\n"

        text += "================================================================\n\n"

    text += "此邮件由Python自动化脚本发送"
    return text

text_body = generate_text_body(current_versions, yesterday_versions, today_str, yesterday_str)

# 生成HTML版本（带版本变化检测）
def generate_html_body(current_versions, yesterday_versions, today_str, yesterday_str):
    html = f"""
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; }}
        .table-container {{ margin: 20px 0; }}
        table {{ border-collapse: collapse; width: 100%; margin: 10px 0; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
        th {{ background-color: #f2f2f2; font-weight: bold; }}
        .table-title {{ font-weight: bold; margin: 20px 0 10px 0; color: #333; }}
        .separator {{ margin: 20px 0; border-top: 2px solid #ccc; }}
        .version-changed {{ background-color: #ffcccc; font-weight: bold; }}
        .legend {{ margin: 10px 0; font-size: 12px; color: #666; }}
    </style>
</head>
<body>
    <p>您好！</p>

    <div class="table-container">
        <div class="table-title">应用版本对比表 - {today_str}</div>
        <div class="legend">注：红色背景表示版本相比昨日发生变化</div>
        <table>
            <thead>
                <tr>
                    <th>应用名称</th>
                    <th>APPLE</th>
                    <th>HUAWEI</th>
                    <th>OPPO</th>
                    <th>VIVO</th>
                    <th>XIAOMI</th>
                    <th>HONOR</th>
                </tr>
            </thead>
            <tbody>
"""

    # 生成当前版本表格行
    for app_name in ['安吉星', 'MyCadillac', 'MyChevy', 'iBuick']:
        html += f"                <tr>\n"
        html += f"                    <td>{app_name}</td>\n"

        current_app_versions = current_versions.get(app_name, {})
        yesterday_app_versions = yesterday_versions.get(app_name, {})

        for platform in ['APPLE', 'HUAWEI', 'OPPO', 'VIVO', 'XIAOMI', 'HONOR']:
            current_ver = current_app_versions.get(platform, '未找到')
            yesterday_ver = yesterday_app_versions.get(platform, '未找到')

            # 检查版本是否发生变化
            is_changed = (current_ver != yesterday_ver and
                         current_ver != '未找到' and
                         yesterday_ver != '未找到')

            css_class = ' class="version-changed"' if is_changed else ''
            html += f"                    <td{css_class}>{current_ver}</td>\n"

        html += f"                </tr>\n"

    html += """            </tbody>
        </table>
    </div>
"""

    # 如果有昨日数据，添加昨日表格
    if yesterday_versions:
        html += f"""
    <div class="separator"></div>

    <div class="table-container">
        <div class="table-title">应用版本对比表 - {yesterday_str}</div>
        <table>
            <thead>
                <tr>
                    <th>应用名称</th>
                    <th>APPLE</th>
                    <th>HUAWEI</th>
                    <th>OPPO</th>
                    <th>VIVO</th>
                    <th>XIAOMI</th>
                    <th>HONOR</th>
                </tr>
            </thead>
            <tbody>
"""

        for app_name in ['安吉星', 'MyCadillac', 'MyChevy', 'iBuick']:
            html += f"                <tr>\n"
            html += f"                    <td>{app_name}</td>\n"

            yesterday_app_versions = yesterday_versions.get(app_name, {})

            for platform in ['APPLE', 'HUAWEI', 'OPPO', 'VIVO', 'XIAOMI', 'HONOR']:
                yesterday_ver = yesterday_app_versions.get(platform, '未找到')
                html += f"                    <td>{yesterday_ver}</td>\n"

            html += f"                </tr>\n"

        html += """            </tbody>
        </table>
    </div>
"""

    html += """
    <p style="margin-top: 30px; color: #666; font-style: italic;">此邮件由Python自动化脚本发送</p>
</body>
</html>
"""
    return html

html_body = generate_html_body(current_versions, yesterday_versions, today_str, yesterday_str)

# 设置邮件内容（支持HTML和纯文本）
msg.set_content(text_body)
msg.add_alternative(html_body, subtype='html')

# 7. 添加附件
attachment_file = f'INFO/info-{today_str}.txt'
if os.path.exists(attachment_file):
    with open(attachment_file, 'r', encoding='utf-8') as f:
        file_content = f.read()
        msg.add_attachment(file_content.encode('utf-8'),
                          maintype='text',
                          subtype='plain',
                          filename=attachment_file)
    print(f"已添加附件: {attachment_file}")
else:
    print(f"警告: 附件文件 {attachment_file} 不存在")

# 8. 发送邮件
try:
    print("正在连接到SMTP服务器...")

    # 方法1：使用SMTP_SSL直接连接
    context = ssl.create_default_context()
    server = smtplib.SMTP_SSL(smtp_server, smtp_port, context=context, timeout=30)

    print("连接成功，正在登录...")
    server.login(sender_email, sender_password)

    print("登录成功，正在发送邮件...")
    server.send_message(msg)

    server.quit()
    print("邮件发送成功！")

except smtplib.SMTPAuthenticationError as e:
    print(f"认证失败：{e}")
    print("请检查邮箱地址和授权码是否正确")
except smtplib.SMTPConnectError as e:
    print(f"连接失败：{e}")
    print("请检查网络连接和SMTP服务器设置")
except smtplib.SMTPException as e:
    print(f"SMTP错误：{e}")
except socket.timeout:
    print("连接超时，请检查网络连接")
except Exception as e:
    print(f"发生未知错误：{e}")
    print("尝试使用备用方法...")

    # 方法2：使用STARTTLS
    try:
        print("尝试使用STARTTLS方法...")
        server = smtplib.SMTP(smtp_server, 587, timeout=30)
        server.starttls(context=ssl.create_default_context())
        server.login(sender_email, sender_password)
        server.send_message(msg)
        server.quit()
        print("邮件发送成功！（使用STARTTLS）")
    except Exception as e2:
        print(f"备用方法也失败了：{e2}")
        print("请检查：")
        print("1. 网络连接是否正常")
        print("2. QQ邮箱是否开启了SMTP服务")
        print("3. 授权码是否正确")
        print("4. 是否被防火墙阻止")
