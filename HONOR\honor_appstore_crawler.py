#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HONOR应用商店版本号快速获取工具
通过ADB命令打开应用详情页，获取XML并解析版本信息

使用方法:
1. 批量模式: python honor_appstore_crawler.py
2. 单个应用: python honor_appstore_crawler.py com.shanghaionstar
3. 测试ADB命令: python honor_appstore_crawler.py test

ADB命令说明:
- 使用 market://details?id=包名 协议打开应用详情页
- 自动检测HONOR应用商店并在其中打开
"""

import subprocess
import time
import os
import re
from datetime import datetime

# 逍遥模拟器路径配置
LD_PATH = r"D:\Program Files\Microvirt\MEmu\MEmu.exe"
LD_CONSOLE_PATH = r"D:\Program Files\Microvirt\MEmu\memuc.exe"


def start_emulator():
    """启动逍遥模拟器"""
    print("启动逍遥模拟器...")
    try:
        # 启动逍遥模拟器实例
        subprocess.Popen([LD_PATH, "MEmu"])
        print("[OK] 模拟器启动命令已发送")

        # 等待模拟器启动并检测ADB设备
        print("等待模拟器启动...")
        max_wait_time = 120  # 最大等待时间120秒
        wait_interval = 5    # 每5秒检查一次

        for i in range(0, max_wait_time, wait_interval):
            time.sleep(wait_interval)
            print(f"检查模拟器状态... ({i + wait_interval}/{max_wait_time}秒)")

            # 检查ADB设备
            try:
                result = subprocess.run(["adb", "devices"], capture_output=True, text=True, timeout=10)
                print(f"ADB设备列表输出: {result.stdout.strip()}")

                if result.returncode == 0:
                    # 检查是否有任何设备连接（包含"device"状态的行）
                    lines = result.stdout.strip().split('\n')
                    device_found = False
                    for line in lines[1:]:  # 跳过第一行标题
                        if line.strip() and '\tdevice' in line:
                            device_name = line.split('\t')[0]
                            print(f"[OK] 检测到设备: {device_name}")
                            device_found = True
                            break

                    if device_found:
                        # 再等待几秒确保系统完全启动
                        print("等待系统完全启动...")
                        time.sleep(10)

                        # 测试ADB连接
                        try:
                            test_result = subprocess.run(["adb", "shell", "getprop", "ro.build.version.release"],
                                                       capture_output=True, text=True, timeout=10)
                            if test_result.returncode == 0:
                                print(f"[OK] 模拟器启动成功，Android版本: {test_result.stdout.strip()}")
                                return True
                        except Exception as e:
                            print(f"ADB连接测试失败: {e}")
                            continue

            except Exception as e:
                print(f"检查ADB设备时出错: {e}")
                continue

        print("[ERROR] 模拟器启动超时")
        return False

    except Exception as e:
        print(f"[ERROR] 启动模拟器失败: {e}")
        return False


def close_emulator():
    """关闭逍遥模拟器"""
    try:
        print("正在关闭逍遥模拟器...")
        result = subprocess.run([LD_CONSOLE_PATH, "stop", "-i", "0"],
                               capture_output=True, text=True, timeout=30)

        if result.returncode == 0:
            print("[OK] 模拟器关闭命令执行成功")
        else:
            print(f"模拟器关闭命令执行失败: {result.stderr}")

        # 等待几秒让模拟器正常关闭
        time.sleep(5)

        # 检查进程是否还在运行
        try:
            result = subprocess.run(["tasklist", "/fi", "imagename eq MEmu.exe"],
                                  capture_output=True, text=True, timeout=10)
            if "MEmu.exe" in result.stdout:
                print("检测到模拟器进程仍在运行，强制关闭...")
                subprocess.run(["taskkill", "/f", "/im", "MEmu.exe"], timeout=10)
                subprocess.run(["taskkill", "/f", "/im", "MEmuHeadless.exe"], timeout=10)
                print("[OK] 强制关闭模拟器进程")
            else:
                print("[OK] 模拟器已正常关闭")
        except Exception as e:
            print(f"检查进程状态时出错: {e}")

    except Exception as e:
        print(f"关闭模拟器时出错: {e}")
        # 如果失败，则强制杀死进程
        try:
            print("尝试强制关闭模拟器进程...")
            subprocess.run(["taskkill", "/f", "/im", "MEmu.exe"], timeout=10)
            subprocess.run(["taskkill", "/f", "/im", "MEmuHeadless.exe"], timeout=10)
            print("[OK] 强制关闭完成")
        except Exception as kill_error:
            print(f"强制关闭进程失败: {kill_error}")


class HonorAppVersionCrawler:
    def __init__(self):
        # 应用包名到应用名称的映射
        self.app_name_mapping = {
            "com.shanghaionstar": "安吉星",
            "com.buick": "iBuick",
            "com.shanghaigm.android.mycadillac": "MyCadillac",
            "com.shanghaigm.mychevy": "MyChevy"
        }

    def execute_adb_command(self, command):
        """执行ADB命令"""
        try:
            result = subprocess.run(command, shell=True, capture_output=True, text=True, encoding='utf-8')
            if result.returncode == 0:
                return result.stdout
            else:
                return None
        except Exception as e:
            print(f"执行命令时发生错误: {e}")
            return None

    def check_adb_connection(self):
        """检查ADB连接状态"""
        print("检查ADB连接状态...")
        result = self.execute_adb_command("adb devices")
        if result:
            print("[OK] ADB连接正常")
            return "device" in result
        return False

    def open_app_details_page(self, package_name):
        """打开应用详情页面"""
        print(f"打开应用详情页面: {package_name}")

        # 使用您提供的market://协议打开应用详情页面
        command = f'adb shell am start -a android.intent.action.VIEW -d "market://details?id={package_name}"'
        result = self.execute_adb_command(command)

        if result is not None:
            # 针对com.shanghaionstar应用等待60秒，其他应用等待5秒
            if package_name == "com.shanghaionstar":
                print("[OK] 页面已打开，等待60秒加载...")
                time.sleep(60)  # 安吉星应用等待60秒
            else:
                print("[OK] 页面已打开，等待加载...")
                time.sleep(5)   # 其他应用等待5秒
            return True
        else:
            print("[ERROR] 打开页面失败")
            return False

    def get_app_version_direct(self, package_name):
        """直接获取应用版本号 - 包含滚动搜索"""
        print(f"获取 {package_name} 版本号...")

        # 先尝试当前页面
        version = self.try_extract_version()
        if version:
            return version

        # 如果当前页面没找到，尝试滚动
        print("当前页面未找到版本号，尝试滚动...")
        for i in range(3):
            print(f"第{i+1}次滚动...")
            # 向下滚动
            self.execute_adb_command("adb shell input swipe 500 1500 500 500 1000")
            time.sleep(1)

            # 再次尝试提取版本号
            version = self.try_extract_version()
            if version:
                print(f"滚动后找到版本号: {version}")
                return version

        print("滚动后仍未找到版本号")
        return None

    def try_extract_version(self):
        """尝试从当前页面提取版本号"""
        try:
            # 获取页面XML
            temp_xml_path = "/sdcard/window_dump.xml"

            # 清理旧文件
            self.execute_adb_command(f"adb shell rm {temp_xml_path}")

            # 执行UI dump
            dump_result = self.execute_adb_command(f"adb shell uiautomator dump {temp_xml_path}")
            if dump_result is None:
                return None

            # 拉取XML文件
            temp_local_path = "temp_ui_dump.xml"
            pull_result = self.execute_adb_command(f"adb pull {temp_xml_path} {temp_local_path}")
            if pull_result is None or not os.path.exists(temp_local_path):
                return None

            # 读取XML内容
            with open(temp_local_path, 'r', encoding='utf-8') as f:
                xml_content = f.read()

            # 清理临时文件
            os.remove(temp_local_path)
            self.execute_adb_command(f"adb shell rm {temp_xml_path}")

            # 查找版本号
            version = self.extract_version_from_xml(xml_content)
            return version

        except Exception as e:
            print(f"提取版本号时发生错误: {e}")
            return None

    def extract_version_from_xml(self, xml_content):
        """从XML中提取版本号 - 适配HONOR应用商店"""
        print("分析XML，查找版本号...")

        # 方法1: 查找HONOR应用商店特定的resource-id和text组合
        # 常见的HONOR应用商店版本号resource-id模式
        honor_resource_patterns = [
            r'text="版本\s*([0-9]+\.[0-9]+(?:\.[0-9]+)*)"[^>]*resource-id="[^"]*version[^"]*"',
            r'resource-id="[^"]*version[^"]*"[^>]*text="版本\s*([0-9]+\.[0-9]+(?:\.[0-9]+)*)"',
            r'text="版本\s*([0-9]+\.[0-9]+(?:\.[0-9]+)*)"[^>]*resource-id="[^"]*app_version[^"]*"',
            r'resource-id="[^"]*app_version[^"]*"[^>]*text="版本\s*([0-9]+\.[0-9]+(?:\.[0-9]+)*)"'
        ]

        for pattern in honor_resource_patterns:
            matches = re.findall(pattern, xml_content)
            if matches:
                version = matches[0]
                print(f"[OK] 通过HONOR resource-id找到版本号: {version}")
                return version

        # 方法2: 查找包含"版本"的text属性
        version_text_patterns = [
            r'text="版本\s*([0-9]+\.[0-9]+(?:\.[0-9]+)*)"',
            r'text="Version\s*([0-9]+\.[0-9]+(?:\.[0-9]+)*)"',
            r'text="v([0-9]+\.[0-9]+(?:\.[0-9]+)*)"'
        ]

        for pattern in version_text_patterns:
            matches = re.findall(pattern, xml_content, re.IGNORECASE)
            if matches:
                version = matches[0]
                print(f"[OK] 通过文本模式找到版本号: {version}")
                return version

        # 方法3: 查找任何符合版本号格式的文本（排除明显错误的）
        general_version_pattern = r'text="([0-9]+\.[0-9]+\.[0-9]+(?:\.[0-9]+)*)"'
        matches = re.findall(general_version_pattern, xml_content)
        if matches:
            # 过滤掉明显不是应用版本的数字
            for match in matches:
                if not match.startswith('1.0') and len(match.split('.')) >= 3:
                    print(f"[OK] 通过通用模式找到版本号: {match}")
                    return match

        print("[ERROR] 未找到版本号")
        return None

    def get_app_version(self, package_name):
        """获取单个应用的版本号"""
        app_name = self.app_name_mapping.get(package_name, package_name)
        print(f"[{app_name}] 开始获取版本号...")

        # 1. 打开应用详情页
        if not self.open_app_details_page(package_name):
            print(f"[ERROR] [{app_name}] 无法打开应用详情页")
            return None

        # 2. 直接获取版本号
        version = self.get_app_version_direct(package_name)

        if version:
            print(f"[OK] [{app_name}] 版本号: {version}")
            return version
        else:
            print(f"[ERROR] [{app_name}] 未找到版本号")
            return None

    def write_log_file(self, app_versions):
        """将结果写入日志文件"""
        current_date = datetime.now()
        log_filename = f"honor_apps_info-{current_date.year}-{current_date.month}-{current_date.day}.log"
        # 直接保存到当前目录
        log_filepath = log_filename

        try:
            with open(log_filepath, 'w', encoding='utf-8') as f:
                f.write("============================================================\n")
                f.write("=== HONOR应用商店版本号获取结果 ===\n")
                f.write("============================================================\n")

                for package_name, version in app_versions.items():
                    app_name = self.app_name_mapping.get(package_name, package_name)
                    if version:
                        f.write(f"[OK] {app_name:<15} 版本: {version}\n")
                    else:
                        f.write(f"[ERROR] {app_name:<15} 版本: 未找到\n")

                f.write("\n")

            print(f"\n[OK] 日志已保存到: {log_filepath}")
            return log_filepath
        except Exception as e:
            print(f"[ERROR] 保存日志文件失败: {e}")
            return None

    def batch_get_versions(self, app_packages):
        """批量获取多个应用的版本号"""
        print("=== HONOR应用商店版本号快速获取工具 ===")
        print(f"目标应用数量: {len(app_packages)}")

        # 检查ADB连接
        if not self.check_adb_connection():
            print("[ERROR] ADB连接失败")
            return {}

        app_versions = {}

        # 依次获取每个应用的版本号
        for i, package_name in enumerate(app_packages, 1):
            print(f"\n[{i}/{len(app_packages)}] {package_name}")
            version = self.get_app_version(package_name)
            app_versions[package_name] = version

            # 应用间等待
            if i < len(app_packages):
                print("等待2秒...")
                time.sleep(2)

        return app_versions

    def print_summary(self, app_versions):
        """打印结果汇总"""
        print("\n" + "="*60)
        print("=== 最终结果汇总 ===")
        print("="*60)

        for package_name, version in app_versions.items():
            app_name = self.app_name_mapping.get(package_name, package_name)
            if version:
                print(f"[OK] {app_name:<15} 版本: {version}")
            else:
                print(f"[ERROR] {app_name:<15} 版本: 未找到")

        print()


def main():
    """主函数"""
    # 要查找的应用包名列表
    app_packages = [
        "com.shanghaionstar",           # 安吉星
        "com.buick",                    # iBuick
        "com.shanghaigm.android.mycadillac",  # MyCadillac
        "com.shanghaigm.mychevy"        # MyChevy
    ]

    # 启动逍遥模拟器
    if not start_emulator():
        print("[ERROR] 模拟器启动失败，程序退出")
        return

    crawler = HonorAppVersionCrawler()

    try:
        # 批量获取版本信息
        app_versions = crawler.batch_get_versions(app_packages)

        # 打印结果汇总
        crawler.print_summary(app_versions)

        # 保存日志文件
        crawler.write_log_file(app_versions)

        print("\n任务完成!")

    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"\n错误: {e}")
    finally:
        # 无论程序是否成功执行，都要关闭模拟器
        close_emulator()


def single_app_mode(package_name):
    """单个应用模式"""
    # 启动逍遥模拟器
    if not start_emulator():
        print("[ERROR] 模拟器启动失败，程序退出")
        return

    crawler = HonorAppVersionCrawler()

    try:
        if not crawler.check_adb_connection():
            print("[ERROR] ADB连接失败")
            return

        version = crawler.get_app_version(package_name)
        app_name = crawler.app_name_mapping.get(package_name, package_name)

        print(f"\n=== 结果 ===")
        if version:
            print(f"[OK] {app_name} 版本: {version}")
        else:
            print(f"[ERROR] {app_name} 未找到版本")

    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"\n错误: {e}")
    finally:
        # 关闭模拟器
        close_emulator()


def test_adb_commands():
    """测试特定的ADB命令"""
    print("=== 测试ADB命令 ===")

    # 启动逍遥模拟器
    if not start_emulator():
        print("[ERROR] 模拟器启动失败，程序退出")
        return

    try:
        # 测试命令列表 - 使用您提供的具体命令
        test_commands = [
            'adb shell am start -a android.intent.action.VIEW -d "market://details?id=com.shanghaionstar"',
            'adb shell am start -a android.intent.action.VIEW -d "market://details?id=com.buick"',
            'adb shell am start -a android.intent.action.VIEW -d "market://details?id=com.shanghaigm.android.mycadillac"',
            'adb shell am start -a android.intent.action.VIEW -d "market://details?id=com.shanghaigm.mychevy"'
        ]

        app_names = ["安吉星", "iBuick", "MyCadillac", "MyChevy"]

        crawler = HonorAppVersionCrawler()

        if not crawler.check_adb_connection():
            print("[ERROR] ADB连接失败")
            return

        for i, (command, app_name) in enumerate(zip(test_commands, app_names)):
            print(f"\n[{i+1}/4] 测试 {app_name}")
            print(f"执行命令: {command}")

            result = crawler.execute_adb_command(command)
            if result is not None:
                print(f"[OK] {app_name} 页面打开成功")
                time.sleep(3)  # 等待页面加载
            else:
                print(f"[ERROR] {app_name} 页面打开失败")

            # 询问是否继续下一个
            if i < len(test_commands) - 1:
                input("按回车键继续下一个应用...")

        print("\n=== 测试完成 ===")

    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"\n错误: {e}")
    finally:
        # 关闭模拟器
        close_emulator()


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        if sys.argv[1] == "test":
            # 测试ADB命令模式
            test_adb_commands()
        else:
            # 单个应用模式
            package_name = sys.argv[1]
            single_app_mode(package_name)
    else:
        # 批量模式
        main()