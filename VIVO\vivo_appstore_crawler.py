#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
vivo应用商店爬虫脚本
用于抓取多个汽车品牌应用的详细信息

目标应用：
1. 安吉星 (OnStar) - App ID: 67018
2. MyCadillac (凯迪拉克) - App ID: 990105
3. <PERSON><PERSON><PERSON><PERSON> (雪佛兰) - App ID: 1621181
4. i<PERSON><PERSON>ck (别克) - App ID: 393987

抓取字段：
1. 版本信息
2. 更新时间

技术栈：Selenium + Chrome WebDriver
"""

import json
import time
import logging
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
import re

# 配置日志 - 只输出到控制台，不生成日志文件
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class VivoAppStoreCrawler:
    """vivo应用商店爬虫类"""

    def __init__(self, headless=True, timeout=30):
        """
        初始化爬虫

        Args:
            headless (bool): 是否使用无头模式
            timeout (int): 页面加载超时时间（秒）
        """
        self.timeout = timeout
        self.driver = None

        # 定义所有目标应用的信息
        self.apps_config = {
            "安吉星": {
                "app_id": "67018",
                "url": "https://h5.appstore.vivo.com.cn/#/details?search_word=%E5%AE%89%E5%90%89%E6%98%9F&search_action=4&app_id=67018&app_pos=1&source=5&appId=67018&frompage=searchResultApp&listpos=1"
            },
            "MyCadillac": {
                "app_id": "990105",
                "url": "https://h5.appstore.vivo.com.cn/#/details?search_word=%E5%87%AF%E8%BF%AA%E6%8B%89%E5%85%8B&search_action=4&app_id=990105&app_pos=1&source=5&appId=990105&frompage=searchResultApp&listpos=1"
            },
            "MyChevy": {
                "app_id": "1621181",
                "url": "https://h5.appstore.vivo.com.cn/#/details?search_word=%E9%9B%AA%E4%BD%9B%E5%85%B0&search_action=4&app_id=1621181&app_pos=1&source=5&appId=1621181&frompage=searchResultApp&listpos=1"
            },
            "iBuick": {
                "app_id": "393987",
                "url": "https://h5.appstore.vivo.com.cn/#/details?search_word=%E5%88%AB%E5%85%8B&search_action=4&app_id=393987&app_pos=1&source=5&appId=393987&frompage=searchResultApp&listpos=1"
            }
        }
        
        # 配置Chrome选项
        self.chrome_options = Options()
        if headless:
            self.chrome_options.add_argument('--headless')
        
        # 反反爬虫设置
        self.chrome_options.add_argument('--no-sandbox')
        self.chrome_options.add_argument('--disable-dev-shm-usage')
        self.chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        self.chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        self.chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # 设置用户代理
        self.chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        # 设置窗口大小
        self.chrome_options.add_argument('--window-size=1920,1080')
        
    def setup_driver(self):
        """设置WebDriver"""
        try:
            logger.info("正在初始化Chrome WebDriver...")

            # 使用webdriver-manager自动管理ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=self.chrome_options)

            # 执行反检测脚本
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            # 设置页面加载超时
            self.driver.set_page_load_timeout(self.timeout)

            # 设置隐式等待
            self.driver.implicitly_wait(10)

            logger.info("WebDriver初始化成功")
            return True

        except WebDriverException as e:
            logger.error(f"WebDriver初始化失败: {e}")
            logger.error("请确保已安装Chrome浏览器")
            return False
    
    def load_page(self, url, app_name):
        """加载目标页面"""
        try:
            logger.info(f"正在加载页面: {app_name} - {url}")
            self.driver.get(url)

            # 等待页面加载完成
            time.sleep(5)

            # 等待关键元素出现
            wait = WebDriverWait(self.driver, self.timeout)

            # 尝试等待版本信息元素出现
            try:
                wait.until(EC.presence_of_element_located((By.XPATH, "//span[contains(@class, 'labelitem') and text()='版本信息']")))
                logger.info(f"{app_name} 页面加载成功，找到版本信息元素")
                return True
            except TimeoutException:
                logger.warning(f"{app_name} 未找到版本信息元素，尝试其他方式验证页面加载")

                # 检查页面内容是否已加载
                if len(self.driver.page_source) > 1000:
                    logger.info(f"{app_name} 页面内容已加载")
                    return True
                else:
                    logger.error(f"{app_name} 页面加载失败或内容不完整")
                    return False

        except Exception as e:
            logger.error(f"{app_name} 页面加载失败: {e}")
            return False
    
    def extract_app_info(self, app_name, url):
        """提取应用信息"""
        try:
            logger.info(f"开始提取 {app_name} 应用信息...")

            app_info = {
                "app_name": app_name,
                "version": None,
                "update_time": None,
                "source_url": url,
                "crawl_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            # 等待页面完全加载
            time.sleep(3)
            
            # 方法1: 使用精确的XPath选择器
            version_selectors = [
                "//p[@data-v-9f4efc36]//span[@class='labelitem' and text()='版本信息']/following-sibling::span",
                "//span[text()='版本信息']/following-sibling::span",
                "//span[contains(@class, 'labelitem') and text()='版本信息']/following-sibling::span",
                "//p[contains(., '版本信息')]//span[not(contains(@class, 'labelitem'))]"
            ]
            
            update_time_selectors = [
                "//p[@data-v-9f4efc36]//span[@class='labelitem' and text()='更新时间']/following-sibling::span",
                "//span[text()='更新时间']/following-sibling::span",
                "//span[contains(@class, 'labelitem') and text()='更新时间']/following-sibling::span",
                "//p[contains(., '更新时间')]//span[not(contains(@class, 'labelitem'))]"
            ]
            
            # 提取版本信息
            for selector in version_selectors:
                try:
                    element = self.driver.find_element(By.XPATH, selector)
                    if element and element.text.strip():
                        app_info["version"] = element.text.strip()
                        logger.info(f"找到版本信息: {app_info['version']}")
                        break
                except NoSuchElementException:
                    continue
            
            # 提取更新时间
            for selector in update_time_selectors:
                try:
                    element = self.driver.find_element(By.XPATH, selector)
                    if element and element.text.strip():
                        app_info["update_time"] = element.text.strip()
                        logger.info(f"找到更新时间: {app_info['update_time']}")
                        break
                except NoSuchElementException:
                    continue
            
            # 方法2: 如果精确选择器失败，使用正则表达式从页面源码中提取
            if not app_info["version"] or not app_info["update_time"]:
                logger.info("尝试从页面源码中提取信息...")
                page_source = self.driver.page_source
                
                # 提取版本信息
                if not app_info["version"]:
                    version_pattern = r'版本信息.*?(\d+\.\d+\.\d+)'
                    version_match = re.search(version_pattern, page_source)
                    if version_match:
                        app_info["version"] = version_match.group(1)
                        logger.info(f"从源码提取版本信息: {app_info['version']}")
                
                # 提取更新时间
                if not app_info["update_time"]:
                    time_pattern = r'更新时间.*?(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})'
                    time_match = re.search(time_pattern, page_source)
                    if time_match:
                        app_info["update_time"] = time_match.group(1)
                        logger.info(f"从源码提取更新时间: {app_info['update_time']}")
            
            return app_info
            
        except Exception as e:
            logger.error(f"提取应用信息失败: {e}")
            return None
    
    def save_result(self, app_info, filename=None):
        """保存结果到JSON文件 - 已禁用，仅保留all_apps_info.json"""
        # 不再保存单个应用的JSON文件
        return True

    def save_all_results(self, all_app_info, filename=None):
        """保存所有应用信息到一个JSON文件"""
        try:
            # 如果没有指定文件名，生成包含当前日期的默认文件名
            if filename is None:
                current_date = datetime.now()
                filename = f"vivo_apps_info-{current_date.year}-{current_date.month}-{current_date.day}.json"

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(all_app_info, f, ensure_ascii=False, indent=2)
            logger.info(f"所有应用信息已保存到: {filename}")
            return True
        except Exception as e:
            logger.error(f"保存所有结果失败: {e}")
            return False
    
    def crawl_single_app(self, app_name):
        """爬取单个应用的信息"""
        try:
            app_config = self.apps_config[app_name]
            url = app_config["url"]

            # 1. 加载页面
            if not self.load_page(url, app_name):
                return None

            # 2. 提取信息
            app_info = self.extract_app_info(app_name, url)

            if app_info:
                # 3. 打印结果
                logger.info("=" * 50)
                logger.info(f"{app_name} 抓取结果:")
                logger.info(json.dumps(app_info, ensure_ascii=False, indent=2))
                logger.info("=" * 50)

                return app_info
            else:
                logger.error(f"未能提取到 {app_name} 的有效信息")
                return None

        except Exception as e:
            logger.error(f"爬取 {app_name} 过程中发生错误: {e}")
            return None

    def crawl_all_apps(self):
        """执行完整的爬取流程 - 爬取所有应用"""
        all_results = {}
        successful_count = 0

        try:
            # 1. 设置WebDriver
            if not self.setup_driver():
                return None

            # 2. 遍历所有应用
            for app_name in self.apps_config.keys():
                logger.info(f"\n开始爬取 {app_name}...")

                result = self.crawl_single_app(app_name)
                if result:
                    all_results[app_name] = result
                    successful_count += 1
                else:
                    all_results[app_name] = {
                        "app_name": app_name,
                        "version": None,
                        "update_time": None,
                        "source_url": self.apps_config[app_name]["url"],
                        "crawl_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "error": "爬取失败"
                    }

                # 在应用之间添加延迟，避免请求过于频繁
                time.sleep(3)

            # 3. 保存所有结果
            if all_results:
                self.save_all_results(all_results)

                # 4. 打印汇总结果
                logger.info("=" * 80)
                logger.info("所有应用爬取结果汇总:")
                logger.info(f"成功: {successful_count}/{len(self.apps_config)}")
                logger.info("=" * 80)

                for app_name, info in all_results.items():
                    status = "✓" if info.get("version") else "✗"
                    version = info.get("version", "获取失败")
                    logger.info(f"{status} {app_name}: {version}")

                logger.info("=" * 80)

                return all_results
            else:
                logger.error("未能提取到任何有效信息")
                return None

        except Exception as e:
            logger.error(f"爬取过程中发生错误: {e}")
            return None

        finally:
            # 清理资源
            if self.driver:
                self.driver.quit()
                logger.info("WebDriver已关闭")


def main():
    """主函数"""
    logger.info("=" * 80)
    logger.info("vivo应用商店爬虫 - 多汽车品牌应用信息抓取")
    logger.info("目标应用: 安吉星、MyCadillac、MyChevy、iBuick")
    logger.info("=" * 80)

    # 创建爬虫实例
    crawler = VivoAppStoreCrawler(headless=True, timeout=30)  # 设置为True以在后台运行

    # 执行爬取所有应用
    result = crawler.crawl_all_apps()

    if result:
        logger.info("所有应用爬取任务完成！")
        return result
    else:
        logger.error("爬取任务失败！")
        return None


if __name__ == "__main__":
    main()
