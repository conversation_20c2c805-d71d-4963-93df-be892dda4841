#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小米应用商店爬虫脚本
用于抓取多个汽车品牌应用的详细信息

目标应用：
1. 安吉星 (OnStar) - Package: com.shanghaionstar
2. MyCadillac (凯迪拉克) - Package: com.shanghaigm.mycadillac
3. MyChevy (雪佛兰) - Package: com.shanghaigm.mychevy
4. iBuick (别克) - Package: com.buick

抓取字段：
1. 版本号
2. 更新时间
3. 包名

技术栈：Selenium + Chrome WebDriver
"""

import json
import time
import logging
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
import re

# 配置日志 - 只输出到控制台，不生成日志文件
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class XiaomiAppStoreCrawler:
    """小米应用商店爬虫类"""
    
    def __init__(self, headless=True, timeout=60):
        """
        初始化爬虫

        Args:
            headless (bool): 是否使用无头模式
            timeout (int): 页面加载超时时间（秒）
        """
        self.timeout = timeout
        self.driver = None

        # 定义所有目标应用的信息
        self.apps_config = {
            "安吉星": {
                "package_name": "com.shanghaionstar",
                "url": "https://app.mi.com/details?id=com.shanghaionstar&ref=search"
            },
            "MyCadillac": {
                "package_name": "com.shanghaigm.android.mycadillac",
                "url": "https://app.mi.com/details?id=com.shanghaigm.android.mycadillac&ref=search"
            },
            "MyChevy": {
                "package_name": "com.shanghaigm.mychevy",
                "url": "https://app.mi.com/details?id=com.shanghaigm.mychevy&ref=search"
            },
            "iBuick": {
                "package_name": "com.buick",
                "url": "https://app.mi.com/details?id=com.buick&ref=search"
            }
        }

        # 配置Chrome选项
        self.chrome_options = Options()
        if headless:
            self.chrome_options.add_argument('--headless')

        # 增强反反爬虫设置
        self.chrome_options.add_argument('--no-sandbox')
        self.chrome_options.add_argument('--disable-dev-shm-usage')
        self.chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        self.chrome_options.add_argument('--disable-web-security')
        self.chrome_options.add_argument('--disable-features=VizDisplayCompositor')
        self.chrome_options.add_argument('--disable-extensions')
        self.chrome_options.add_argument('--disable-plugins')
        self.chrome_options.add_argument('--disable-images')  # 禁用图片加载以提高速度
        # 不禁用JavaScript，因为页面可能需要JS来正确显示内容
        self.chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        self.chrome_options.add_experimental_option('useAutomationExtension', False)

        # 设置更真实的用户代理
        self.chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36')

        # 设置窗口大小
        self.chrome_options.add_argument('--window-size=1920,1080')

        # 设置页面加载策略
        self.chrome_options.add_argument('--page-load-strategy=eager')
        
    def setup_driver(self):
        """设置WebDriver"""
        try:
            logger.info("正在初始化Chrome WebDriver...")

            # 使用webdriver-manager自动管理ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=self.chrome_options)

            # 执行反检测脚本
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            # 设置页面加载超时
            self.driver.set_page_load_timeout(self.timeout)

            # 设置隐式等待
            self.driver.implicitly_wait(10)

            logger.info("WebDriver初始化成功")
            return True

        except WebDriverException as e:
            logger.error(f"WebDriver初始化失败: {e}")
            logger.error("请确保已安装Chrome浏览器")
            return False
    
    def load_page(self, url, app_name):
        """加载目标页面"""
        try:
            logger.info(f"正在加载页面: {app_name} - {url}")

            # 设置更长的页面加载超时
            self.driver.set_page_load_timeout(self.timeout)

            # 加载页面
            self.driver.get(url)

            # 等待页面基本加载完成
            time.sleep(8)

            # 检查页面是否成功加载
            page_source = self.driver.page_source

            # 检查页面内容
            if len(page_source) > 5000:  # 增加最小页面大小要求
                logger.info(f"{app_name} 页面内容已加载，页面大小: {len(page_source)} 字符")

                # 检查是否包含关键信息
                if "版本号" in page_source or "更新时间" in page_source:
                    logger.info(f"{app_name} 页面包含目标信息")
                    return True
                else:
                    logger.warning(f"{app_name} 页面已加载但可能缺少目标信息")
                    return True  # 仍然尝试提取信息
            else:
                logger.error(f"{app_name} 页面加载失败或内容不完整，页面大小: {len(page_source)} 字符")
                return False

        except Exception as e:
            logger.error(f"{app_name} 页面加载失败: {e}")
            return False
    
    def extract_app_info(self, app_name, url):
        """提取应用信息"""
        try:
            logger.info(f"开始提取 {app_name} 应用信息...")

            app_info = {
                "app_name": app_name,
                "version": None,
                "update_time": None,
                "package_name": self.apps_config[app_name]["package_name"],
                "source_url": url,
                "crawl_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            # 等待页面完全加载
            time.sleep(3)

            # 获取页面源码
            page_source = self.driver.page_source
            logger.info(f"页面源码长度: {len(page_source)} 字符")

            # 方法1: 直接从页面源码中使用正则表达式提取（更可靠）
            logger.info("使用正则表达式从页面源码中提取信息...")

            # 提取版本信息 - 更精确的正则表达式
            version_patterns = [
                r'版本号\s*</div>\s*<div[^>]*>\s*([0-9]+\.[0-9]+\.[0-9]+)',  # 匹配HTML结构
                r'版本号.*?([0-9]+\.[0-9]+\.[0-9]+)',
                r'版本\s*([0-9]+\.[0-9]+\.[0-9]+)',
                r'Version\s*([0-9]+\.[0-9]+\.[0-9]+)'
            ]

            for pattern in version_patterns:
                version_match = re.search(pattern, page_source, re.IGNORECASE | re.DOTALL)
                if version_match:
                    app_info["version"] = version_match.group(1)
                    logger.info(f"从源码提取版本信息: {app_info['version']}")
                    break

            # 提取更新时间 - 更精确的正则表达式
            time_patterns = [
                r'更新时间\s*</div>\s*<div[^>]*>\s*([0-9]{4}-[0-9]{2}-[0-9]{2})',  # 匹配HTML结构
                r'更新时间.*?([0-9]{4}-[0-9]{2}-[0-9]{2})',
                r'更新.*?([0-9]{4}-[0-9]{2}-[0-9]{2})',
                r'Update.*?([0-9]{4}-[0-9]{2}-[0-9]{2})'
            ]

            for pattern in time_patterns:
                time_match = re.search(pattern, page_source, re.IGNORECASE | re.DOTALL)
                if time_match:
                    app_info["update_time"] = time_match.group(1)
                    logger.info(f"从源码提取更新时间: {app_info['update_time']}")
                    break

            # 方法2: 如果正则表达式失败，尝试使用Selenium元素定位
            if not app_info["version"] or not app_info["update_time"]:
                logger.info("正则表达式提取失败，尝试使用Selenium元素定位...")

                # 重新启用JavaScript（如果之前禁用了）
                try:
                    # 尝试多种XPath选择器
                    version_selectors = [
                        "//div[contains(text(),'版本号')]/following-sibling::div",
                        "//div[text()='版本号']/following-sibling::div",
                        "//td[contains(text(),'版本号')]/following-sibling::td",
                        "//*[contains(text(),'版本号')]/following-sibling::*",
                        "//*[contains(text(),'版本号')]/parent::*/following-sibling::*"
                    ]

                    update_time_selectors = [
                        "//div[contains(text(),'更新时间')]/following-sibling::div",
                        "//div[text()='更新时间']/following-sibling::div",
                        "//td[contains(text(),'更新时间')]/following-sibling::td",
                        "//*[contains(text(),'更新时间')]/following-sibling::*",
                        "//*[contains(text(),'更新时间')]/parent::*/following-sibling::*"
                    ]

                    # 提取版本信息
                    if not app_info["version"]:
                        for selector in version_selectors:
                            try:
                                element = self.driver.find_element(By.XPATH, selector)
                                if element and element.text.strip():
                                    app_info["version"] = element.text.strip()
                                    logger.info(f"通过Selenium找到版本信息: {app_info['version']}")
                                    break
                            except NoSuchElementException:
                                continue

                    # 提取更新时间
                    if not app_info["update_time"]:
                        for selector in update_time_selectors:
                            try:
                                element = self.driver.find_element(By.XPATH, selector)
                                if element and element.text.strip():
                                    app_info["update_time"] = element.text.strip()
                                    logger.info(f"通过Selenium找到更新时间: {app_info['update_time']}")
                                    break
                            except NoSuchElementException:
                                continue

                except Exception as selenium_error:
                    logger.warning(f"Selenium元素定位失败: {selenium_error}")

            # 验证提取结果
            if app_info["version"] or app_info["update_time"]:
                logger.info(f"成功提取 {app_name} 信息 - 版本: {app_info['version']}, 更新时间: {app_info['update_time']}")
            else:
                logger.warning(f"未能提取到 {app_name} 的版本或更新时间信息")

            return app_info

        except Exception as e:
            logger.error(f"提取应用信息失败: {e}")
            return None
    
    def save_result(self, app_info, filename=None):
        """保存结果到JSON文件 - 已禁用，仅保留xiaomi_apps_info.json"""
        # 不再保存单个应用的JSON文件
        return True

    def save_all_results(self, all_app_info, filename=None):
        """保存所有应用信息到一个JSON文件"""
        try:
            # 如果没有指定文件名，生成包含当前日期的默认文件名
            if filename is None:
                current_date = datetime.now()
                filename = f"xiaomi_apps_info-{current_date.year}-{current_date.month}-{current_date.day}.json"

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(all_app_info, f, ensure_ascii=False, indent=2)
            logger.info(f"所有应用信息已保存到: {filename}")
            return True
        except Exception as e:
            logger.error(f"保存所有结果失败: {e}")
            return False

    def crawl_single_app(self, app_name):
        """爬取单个应用的信息"""
        try:
            app_config = self.apps_config[app_name]
            url = app_config["url"]

            # 1. 加载页面
            if not self.load_page(url, app_name):
                return None

            # 2. 提取信息
            app_info = self.extract_app_info(app_name, url)

            if app_info:
                # 3. 打印结果
                logger.info("=" * 50)
                logger.info(f"{app_name} 抓取结果:")
                logger.info(json.dumps(app_info, ensure_ascii=False, indent=2))
                logger.info("=" * 50)

                return app_info
            else:
                logger.error(f"未能提取到 {app_name} 的有效信息")
                return None

        except Exception as e:
            logger.error(f"爬取 {app_name} 过程中发生错误: {e}")
            return None

    def crawl_all_apps(self):
        """执行完整的爬取流程 - 爬取所有应用"""
        all_results = {}
        successful_count = 0

        try:
            # 1. 设置WebDriver
            if not self.setup_driver():
                return None

            # 2. 遍历所有应用
            for app_name in self.apps_config.keys():
                logger.info(f"\n开始爬取 {app_name}...")

                result = self.crawl_single_app(app_name)
                if result:
                    all_results[app_name] = result
                    successful_count += 1
                else:
                    all_results[app_name] = {
                        "app_name": app_name,
                        "version": None,
                        "update_time": None,
                        "package_name": self.apps_config[app_name]["package_name"],
                        "source_url": self.apps_config[app_name]["url"],
                        "crawl_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "error": "爬取失败"
                    }

                # 在应用之间添加延迟，避免请求过于频繁
                time.sleep(3)

            # 3. 保存所有结果
            if all_results:
                self.save_all_results(all_results)

                # 4. 打印汇总结果
                logger.info("=" * 80)
                logger.info("所有应用爬取结果汇总:")
                logger.info(f"成功: {successful_count}/{len(self.apps_config)}")
                logger.info("=" * 80)

                for app_name, info in all_results.items():
                    status = "✓" if info.get("version") else "✗"
                    version = info.get("version", "获取失败")
                    package = info.get("package_name", "未知")
                    update_time = info.get("update_time", "获取失败")
                    logger.info(f"{status} {app_name}: 版本={version}, 包名={package}, 更新时间={update_time}")

                logger.info("=" * 80)

                return all_results
            else:
                logger.error("未能提取到任何有效信息")
                return None

        except Exception as e:
            logger.error(f"爬取过程中发生错误: {e}")
            return None

        finally:
            # 清理资源
            if self.driver:
                self.driver.quit()
                logger.info("WebDriver已关闭")


def main():
    """主函数"""
    logger.info("=" * 80)
    logger.info("小米应用商店爬虫 - 多汽车品牌应用信息抓取")
    logger.info("目标应用: 安吉星、MyCadillac、MyChevy、iBuick")
    logger.info("=" * 80)

    # 创建爬虫实例
    crawler = XiaomiAppStoreCrawler(headless=False, timeout=60)  # 设置为False以便调试，增加超时时间

    # 执行爬取所有应用
    result = crawler.crawl_all_apps()

    if result:
        logger.info("所有应用爬取任务完成！")
        return result
    else:
        logger.error("爬取任务失败！")
        return None


if __name__ == "__main__":
    main()
