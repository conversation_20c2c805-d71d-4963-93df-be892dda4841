import uiautomator2 as u2
import subprocess
import time
import re
import os
from datetime import datetime

# 雷电模拟器路径配置
ld_path = r"D:\Program Files\leidian\LDPlayer9\dnplayer.exe"
ld_console_path = r"D:\Program Files\leidian\LDPlayer9\ldconsole.exe"

def start_emulator():
    """启动模拟器"""
    print("启动雷电模拟器...")
    try:
        # 启动第一个模拟器实例
        subprocess.Popen([ld_path, "index=1"])
        print("[OK] 模拟器启动命令已发送")

        # 等待模拟器启动并检测ADB设备
        print("等待模拟器启动...")
        max_wait_time = 120  # 最大等待时间120秒
        wait_interval = 5    # 每5秒检查一次

        for i in range(0, max_wait_time, wait_interval):
            time.sleep(wait_interval)
            print(f"检查模拟器状态... ({i + wait_interval}/{max_wait_time}秒)")

            # 检查ADB设备
            try:
                result = subprocess.run(["adb", "devices"], capture_output=True, text=True, timeout=10)
                if result.returncode == 0 and "emulator" in result.stdout:
                    print("[OK] 检测到模拟器设备")

                    # 再等待几秒确保系统完全启动
                    print("等待系统完全启动...")
                    time.sleep(10)

                    # 尝试连接uiautomator2
                    try:
                        d = u2.connect()
                        device_info = d.info
                        print(f"[OK] 模拟器启动成功，设备信息: {device_info.get('productName', 'Unknown')}")
                        return True
                    except Exception as e:
                        print(f"uiautomator2连接测试失败: {e}")
                        continue

            except Exception as e:
                print(f"检查ADB设备时出错: {e}")
                continue

        print("[ERROR] 模拟器启动超时")
        return False

    except Exception as e:
        print(f"[ERROR] 启动模拟器失败: {e}")
        return False

def close_emulator():
    """关闭模拟器"""
    try:
        print("正在关闭雷电模拟器...")
        result = subprocess.run([ld_console_path, "quit", "--index", "1"],
                               capture_output=True, text=True, timeout=30)

        if result.returncode == 0:
            print("[OK] 模拟器关闭命令执行成功")
        else:
            print(f"模拟器关闭命令执行失败: {result.stderr}")

        # 等待几秒让模拟器正常关闭
        time.sleep(5)

        # 检查进程是否还在运行
        try:
            result = subprocess.run(["tasklist", "/fi", "imagename eq dnplayer.exe"],
                                  capture_output=True, text=True, timeout=10)
            if "dnplayer.exe" in result.stdout:
                print("检测到模拟器进程仍在运行，强制关闭...")
                subprocess.run(["taskkill", "/f", "/im", "dnplayer.exe"], timeout=10)
                subprocess.run(["taskkill", "/f", "/im", "LdVBoxHeadless.exe"], timeout=10)
                print("[OK] 强制关闭模拟器进程")
            else:
                print("[OK] 模拟器已正常关闭")
        except Exception as e:
            print(f"检查进程状态时出错: {e}")

    except Exception as e:
        print(f"关闭模拟器时出错: {e}")
        # 如果失败，则强制杀死进程
        try:
            print("尝试强制关闭模拟器进程...")
            subprocess.run(["taskkill", "/f", "/im", "dnplayer.exe"], timeout=10)
            subprocess.run(["taskkill", "/f", "/im", "LdVBoxHeadless.exe"], timeout=10)
            print("[OK] 强制关闭完成")
        except Exception as kill_error:
            print(f"强制关闭进程失败: {kill_error}")

def execute_adb_command(command):
    """执行ADB命令"""
    try:
        print(f"执行命令: {command}")
        result = subprocess.run(command, shell=True, capture_output=True, text=True, encoding='utf-8')
        if result.returncode == 0:
            print("[OK] 命令执行成功")
            if result.stdout.strip():
                print(f"输出: {result.stdout.strip()}")
            return True
        else:
            print(f"[ERROR] 命令执行失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"[ERROR] 执行命令时出错: {e}")
        return False

def get_page_xml(d):
    """获取当前页面的XML结构"""
    try:
        print("正在获取页面XML结构...")
        xml_content = d.dump_hierarchy()
        print("[OK] 成功获取XML结构")
        return xml_content
    except Exception as e:
        print(f"[ERROR] 获取XML结构失败: {e}")
        return None



def find_version_in_xml(xml_content):
    """在XML中查找版本号相关信息"""
    print("正在分析XML结构，查找版本号...")

    # 常见的版本号模式
    version_patterns = [
        r'版本([0-9]+\.[0-9]+(?:\.[0-9]+)*(?:\.[0-9]+)*)',  # 版本10.5.2
        r'Version[：:\s]*([0-9]+\.[0-9]+(?:\.[0-9]+)*(?:\.[0-9]+)*)',
        r'v([0-9]+\.[0-9]+(?:\.[0-9]+)*(?:\.[0-9]+)*)',
        r'text="([0-9]+\.[0-9]+\.[0-9]+(?:\.[0-9]+)*)"',  # 直接在text属性中的版本号
        r'text="([0-9]+\.[0-9]+(?:\.[0-9]+)*)"'  # 简化版本号
    ]

    # 查找包含版本信息的节点，排除XML声明
    version_keywords = ['版本', 'version', 'Version', 'VERSION', 'ver', 'Ver']

    found_versions = []

    # 按行分析XML
    lines = xml_content.split('\n')
    for i, line in enumerate(lines):
        # 跳过XML声明行
        if line.strip().startswith('<?xml'):
            continue

        # 检查是否包含版本关键词
        if any(keyword in line for keyword in version_keywords):
            print(f"找到包含版本信息的行 {i+1}: {line.strip()}")

            # 尝试提取版本号
            for pattern in version_patterns:
                matches = re.findall(pattern, line, re.IGNORECASE)
                if matches:
                    for match in matches:
                        # 过滤掉明显不是应用版本号的内容
                        if match not in found_versions and not match.startswith('1.0'):
                            found_versions.append(match)
                            print(f"[OK] 提取到版本号: {match}")

    return found_versions

def find_version_with_uiautomator(d):
    """使用uiautomator2直接查找版本号"""
    print("使用uiautomator2查找版本号...")

    # 常见的版本号相关的resource-id
    version_resource_ids = [
        "com.huawei.appmarket:id/version_info",
        "com.huawei.appmarket:id/version",
        "com.huawei.appmarket:id/app_version",
        "com.huawei.appmarket:id/version_text",
        "com.huawei.appmarket:id/version_name",
        "com.huawei.appmarket:id/app_version_name"
    ]

    found_versions = []

    # 尝试通过resource-id查找
    for resource_id in version_resource_ids:
        try:
            if d(resourceId=resource_id).exists(timeout=2):
                version_text = d(resourceId=resource_id).get_text()
                if version_text and version_text.strip():
                    print(f"[OK] 通过 {resource_id} 找到版本信息: {version_text}")
                    found_versions.append(version_text)
        except Exception as e:
            print(f"查找 {resource_id} 时出错: {e}")

    # 尝试通过文本内容查找
    version_text_patterns = ["版本", "Version", "版本号"]
    for pattern in version_text_patterns:
        try:
            if d(textContains=pattern).exists(timeout=2):
                elements = d(textContains=pattern)
                for i in range(elements.count):
                    element_text = elements[i].get_text()
                    if element_text and element_text.strip():
                        print(f"[OK] 通过文本 '{pattern}' 找到: {element_text}")
                        found_versions.append(element_text)
        except Exception as e:
            print(f"查找包含 '{pattern}' 的文本时出错: {e}")

    return found_versions

def get_app_version(app_package, d):
    """获取单个应用的版本号"""
    print(f"\n=== 正在获取 {app_package} 的版本信息 ===")

    # 1. 执行ADB命令打开应用详情页
    adb_command = f'adb shell am start -n com.huawei.appmarket/.service.externalapi.view.ThirdApiActivity -a android.intent.action.VIEW -d "appmarket://details?id={app_package}"'

    if not execute_adb_command(adb_command):
        print(f"[ERROR] 无法打开 {app_package} 的应用详情页")
        return None

    print("等待页面加载...")
    time.sleep(5)

    # 2. 获取页面XML结构并查找版本号
    xml_content = get_page_xml(d)
    xml_versions = []
    if xml_content:
        # 在XML中查找版本号
        xml_versions = find_version_in_xml(xml_content)

        if xml_versions:
            print(f"从XML中找到的版本号: {xml_versions}")
        else:
            print("在XML中未找到版本号")

    # 3. 使用uiautomator2直接查找版本号
    ui_versions = find_version_with_uiautomator(d)

    # 4. 汇总结果
    all_versions = []

    if xml_content and xml_versions:
        for version in xml_versions:
            if version not in all_versions:
                all_versions.append(version)

    if ui_versions:
        for version in ui_versions:
            if version not in all_versions:
                all_versions.append(version)

    # 返回最可能的版本号（通常是最长的那个，排除明显错误的）
    if all_versions:
        # 过滤版本号，选择最合理的
        filtered_versions = []
        for version in all_versions:
            # 移除"版本"前缀
            clean_version = version.replace("版本", "").strip()
            # 只保留符合版本号格式的
            if re.match(r'^[0-9]+\.[0-9]+(?:\.[0-9]+)*$', clean_version):
                filtered_versions.append(clean_version)

        if filtered_versions:
            # 返回最长的版本号（通常更准确）
            best_version = max(filtered_versions, key=len)
            print(f"[OK] {app_package} 版本号: {best_version}")
            return best_version
        else:
            print(f"[ERROR] {app_package} 未找到有效版本号")
            return None
    else:
        print(f"[ERROR] {app_package} 未找到任何版本信息")
        return None

def write_log_file(app_versions):
    """将结果写入日志文件"""
    # 获取当前日期
    current_date = datetime.now()
    log_filename = f"huawei_apps_info-{current_date.year}-{current_date.month}-{current_date.day}.log"

    # 应用包名到应用名称的映射
    app_name_mapping = {
        "com.shanghaionstar": "安吉星",
        "com.buick": "iBuick",
        "com.shanghaigm.android.mycadillac": "MyCadillac",
        "com.shanghaigm.mychevy": "MyChevy"
    }

    try:
        with open(log_filename, 'w', encoding='utf-8') as f:
            f.write("============================================================\n")
            f.write("=== 最终结果汇总 ===\n")
            f.write("============================================================\n")

            for app_package, version in app_versions.items():
                app_name = app_name_mapping.get(app_package, app_package)
                if version:
                    f.write(f"[OK] {app_name:<14} 版本: {version} \n")
                else:
                    f.write(f"[ERROR] {app_name:<14} 版本: 未找到 \n")
            f.write("\n")

        print(f"\n[OK] 日志已保存到: {log_filename}")
        return log_filename
    except Exception as e:
        print(f"[ERROR] 保存日志文件失败: {e}")
        return None

def main():
    # 要查找的应用包名列表
    app_packages = [
        "com.shanghaionstar",
        "com.buick",
        "com.shanghaigm.android.mycadillac",
        "com.shanghaigm.mychevy"
    ]

    print("=== 华为应用市场应用详情页版本号批量获取工具 ===")
    print(f"目标应用数量: {len(app_packages)}")
    print()

    # 启动雷电模拟器
    if not start_emulator():
        print("[ERROR] 模拟器启动失败，程序退出")
        return

    try:
        # 连接设备
        try:
            print("正在连接设备...")
            d = u2.connect()
            print("[OK] 设备连接成功")
        except Exception as e:
            print(f"[ERROR] 设备连接失败: {e}")
            return

        # 存储所有应用的版本信息
        app_versions = {}

        # 依次获取每个应用的版本号
        for i, app_package in enumerate(app_packages, 1):
            print(f"\n[{i}/{len(app_packages)}] 处理应用: {app_package}")
            version = get_app_version(app_package, d)
            app_versions[app_package] = version

            # 如果不是最后一个应用，稍作等待
            if i < len(app_packages):
                print("等待2秒后处理下一个应用...")
                time.sleep(2)

        # 最终结果汇总
        print("\n" + "="*60)
        print("=== 最终结果汇总 ===")
        print("="*60)

        for app_package, version in app_versions.items():
            if version:
                print(f"[OK] {app_package:<40} 版本: {version}")
            else:
                print(f"[ERROR] {app_package:<40} 版本: 未找到")

        # 统计
        found_count = sum(1 for v in app_versions.values() if v is not None)
        print(f"\n总计: {found_count}/{len(app_packages)} 个应用找到版本信息")

        if found_count < len(app_packages):
            print("\n未找到版本信息的应用可能原因:")
            print("  1. 应用不存在于华为应用市场")
            print("  2. 应用详情页加载失败")
            print("  3. 应用详情页格式发生变化")

        # 写入日志文件
        write_log_file(app_versions)

    finally:
        # 无论程序是否成功执行，都要关闭模拟器
        close_emulator()

if __name__ == "__main__":
    main()